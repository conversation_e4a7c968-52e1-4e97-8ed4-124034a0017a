import type { ActionFunctionArgs } from "@remix-run/cloudflare";
import { redirect } from "@remix-run/cloudflare";
import { authenticator } from "~/services/auth.server";

export async function action({ request }: ActionFunctionArgs) {
  try {
    // Authenticate with Google One Tap
    const user = await authenticator.authenticate("google-one-tap", request, {
      throwOnError: true,
    });

    // Create session and redirect
    return authenticator.authenticate("google-one-tap", request, {
      successRedirect: "/dashboard",
      failureRedirect: "/login?error=auth-failed",
    });
  } catch (error) {
    console.error("Google authentication error:", error);
    return redirect("/login?error=auth-failed");
  }
}

// Handle GET requests (shouldn't happen, but just in case)
export async function loader() {
  return redirect("/login");
}
