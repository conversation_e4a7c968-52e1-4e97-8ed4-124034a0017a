import { Authenticator } from "remix-auth";
import { GoogleCredentialStrategy } from "remix-auth-google-credential";
import { sessionStorage } from "~/services/session.server";
import { db } from "~/lib/db/db";
import { users, accounts } from "~/lib/db/schemas/users";
import { eq } from "drizzle-orm";

export interface SessionUser {
  id: string;
  email: string;
  name?: string;
  avatar?: string;
}

// Create an instance of the authenticator
export const authenticator = new Authenticator<SessionUser>(sessionStorage);

// Configure Google One Tap strategy
authenticator.use(
  new GoogleCredentialStrategy(
    {
      clientID: process.env.GOOGLE_CLIENT_ID!,
    },
    async ({ email, sub, name, picture }) => {
      // Check if user exists by email
      const existingUser = await db
        .select()
        .from(users)
        .where(eq(users.email, email))
        .limit(1);

      let user;

      if (existingUser.length > 0) {
        // Update existing user
        user = existingUser[0];

        // Update user info
        await db
          .update(users)
          .set({
            name: name || user.name,
            avatar: picture || user.avatar,
            lastLoginAt: new Date(),
          })
          .where(eq(users.id, user.id));

        // Check if Google account is linked
        const existingAccount = await db
          .select()
          .from(accounts)
          .where(eq(accounts.userId, user.id))
          .where(eq(accounts.provider, "google"))
          .limit(1);

        if (existingAccount.length === 0) {
          // Link Google account
          await db.insert(accounts).values({
            userId: user.id,
            provider: "google",
            providerUserId: sub,
          });
        }
      } else {
        // Create new user
        const [newUser] = await db
          .insert(users)
          .values({
            email,
            name: name || email.split("@")[0],
            avatar: picture,
            emailVerified: true,
            lastLoginAt: new Date(),
          })
          .returning();

        user = newUser;

        // Create Google account link
        await db.insert(accounts).values({
          userId: user.id,
          provider: "google",
          providerUserId: sub,
        });
      }

      return {
        id: user.id,
        email: user.email,
        name: user.name,
        avatar: user.avatar,
      };
    }
  ),
  "google-one-tap"
);
