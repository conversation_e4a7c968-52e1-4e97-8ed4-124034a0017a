import { Authenticator } from "remix-auth";
import { GoogleCredentialStrategy } from "remix-auth-google-credential";
import { sessionStorage } from "~/services/session.server";
import { db } from "~/services/db.server";

export interface SessionUser {
  id: string;
  email: string;
  name?: string;
  avatar_url?: string;
}

// Create an instance of the authenticator
export const authenticator = new Authenticator<SessionUser>(sessionStorage);

// Configure Google One Tap strategy
authenticator.use(
  new GoogleCredentialStrategy(
    {
      clientID: process.env.GOOGLE_CLIENT_ID!,
    },
    async ({ email, sub, name, picture }) => {
      // Upsert user in database
      const [user] = await db`
        INSERT INTO users (email, google_sub, name, avatar_url)
        VALUES (${email}, ${sub}, ${name}, ${picture})
        ON CONFLICT (google_sub) 
        DO UPDATE SET 
          name = EXCLUDED.name,
          avatar_url = EXCLUDED.avatar_url
        RETURNING id, email, name, avatar_url
      `;

      return {
        id: user.id,
        email: user.email,
        name: user.name,
        avatar_url: user.avatar_url,
      };
    }
  ),
  "google-one-tap"
);
