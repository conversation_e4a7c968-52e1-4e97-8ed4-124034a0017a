import { neon } from "@neondatabase/serverless";

// Initialize Neon database connection
export const db = neon(process.env.NEON_DATABASE_URL!);

// Database schema types
export interface User {
  id: string;
  email: string;
  google_sub?: string;
  name?: string;
  avatar_url?: string;
  password_hash?: string;
  created_at: Date;
}

export interface Session {
  id: string;
  user_id: string;
  expires_at: Date;
  created_at: Date;
}

// Helper functions
export async function createSession(userId: string, expiresInDays = 30) {
  const expiresAt = new Date();
  expiresAt.setDate(expiresAt.getDate() + expiresInDays);

  const [session] = await db`
    INSERT INTO sessions (user_id, expires_at)
    VALUES (${userId}, ${expiresAt})
    RETURNING id, user_id, expires_at, created_at
  `;

  return session;
}

export async function getSessionWithUser(sessionId: string) {
  const [result] = await db`
    SELECT 
      s.id as session_id,
      s.expires_at,
      u.id as user_id,
      u.email,
      u.name,
      u.avatar_url
    FROM sessions s
    JOIN users u ON s.user_id = u.id
    WHERE s.id = ${sessionId} 
    AND s.expires_at > NOW()
  `;

  return result;
}

export async function deleteSession(sessionId: string) {
  await db`DELETE FROM sessions WHERE id = ${sessionId}`;
}
