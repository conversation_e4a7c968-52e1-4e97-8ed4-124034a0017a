import { neon } from "@neondatabase/serverless";
import { drizzle, type NeonHttpDatabase } from "drizzle-orm/neon-http";
import type { PgTransaction } from "drizzle-orm/pg-core";
import * as schema from "./schema";

// Database configuration interface
export interface DatabaseConfig {
  url: string;
  maxConnections?: number;
  connectionTimeoutMillis?: number;
  idleTimeoutMillis?: number;
  enableLogging?: boolean;
}

// Default configuration
const DEFAULT_CONFIG: Partial<DatabaseConfig> = {
  maxConnections: 10,
  connectionTimeoutMillis: 30000, // 30 seconds
  idleTimeoutMillis: 600000, // 10 minutes
  enableLogging: false, // Default to false, will be overridden by environment-specific config
};

// Custom logger for database operations
class DatabaseLogger {
  private enabled: boolean;

  constructor(enabled = false) {
    this.enabled = enabled;
  }

  logQuery(query: string, params: unknown[]) {
    if (this.enabled) {
      console.log("🔍 Database Query:", query);
      if (params.length > 0) {
        console.log("📋 Parameters:", params);
      }
    }
  }
}

// Enhanced database creation function
export function createDb(config: DatabaseConfig | string) {
  let dbConfig: DatabaseConfig;

  // Handle both string URL and config object
  if (typeof config === "string") {
    dbConfig = { url: config, ...DEFAULT_CONFIG };
  } else {
    dbConfig = { ...DEFAULT_CONFIG, ...config };
  }

  // Validate database URL
  if (!dbConfig.url) {
    throw new Error("Database URL is required and cannot be undefined, null, or empty.");
  }

  try {
    // Validate URL format
    new URL(dbConfig.url);
  } catch (error) {
    throw new Error(
      `Invalid database URL format: ${error instanceof Error ? error.message : "Unknown error"}`
    );
  }

  try {
    // Create Neon connection with configuration
    const sql = neon(dbConfig.url);

    // Create Drizzle instance with enhanced configuration
    const db = drizzle(sql, {
      schema,
      logger: dbConfig.enableLogging,
    });

    // Add custom methods to the database instance
    return Object.assign(db, {
      // Health check method
      async healthCheck(): Promise<{ status: "healthy" | "unhealthy"; timestamp: Date }> {
        try {
          await sql`SELECT 1`;
          return { status: "healthy", timestamp: new Date() };
        } catch (error) {
          console.error("Database health check failed:", error);
          return { status: "unhealthy", timestamp: new Date() };
        }
      },

      // Get database configuration
      getConfig(): DatabaseConfig {
        return dbConfig;
      },
    });
  } catch (error) {
    throw new Error(
      `Failed to create database connection: ${error instanceof Error ? error.message : "Unknown error"}`
    );
  }
}

// Type for the enhanced database instance
export type Database = ReturnType<typeof createDb>;

// Utility function to create database with environment variables
export function createDbFromEnv(env?: Record<string, string | undefined> | any): Database {
  // In Cloudflare Workers, env comes from the context
  // In development with Wrangler, process.env is available
  const databaseUrl =
    env?.DATABASE_URL ||
    (typeof process !== "undefined" && process.env ? process.env.DATABASE_URL : undefined);

  if (!databaseUrl) {
    throw new Error("DATABASE_URL environment variable is required but not found");
  }

  // Determine if we're in development mode
  const isDevelopment =
    env?.NODE_ENV === "development" ||
    (typeof process !== "undefined" && process.env
      ? process.env.NODE_ENV === "development"
      : false);

  return createDb({
    url: databaseUrl,
    enableLogging: isDevelopment,
  });
}

// Connection pool manager (for advanced use cases)
export class DatabasePool {
  private static instance: Database | null = null;
  private static config: DatabaseConfig | null = null;

  static getInstance(config?: DatabaseConfig | string): Database {
    if (!DatabasePool.instance || (config && config !== DatabasePool.config)) {
      DatabasePool.instance = createDb(config || DatabasePool.config!);
      DatabasePool.config = typeof config === "string" ? { url: config } : config || null;
    }
    return DatabasePool.instance;
  }

  static async closeConnection(): Promise<void> {
    if (DatabasePool.instance) {
      // Neon serverless connections are automatically managed
      // No explicit close needed, but we can reset the instance
      DatabasePool.instance = null;
      DatabasePool.config = null;
    }
  }
}

// Export commonly used types and classes
export { DatabaseLogger };

// Global database instance for the application
export const db = createDbFromEnv();
